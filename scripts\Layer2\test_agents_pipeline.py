from playsound import playsound
import asyncio
from core.state_mgmt.memory_manager import MemoryManager
from agents.Layer2.stt_agent import STTAgent
from agents.Layer2.preprocessing_agent import PreprocessingAgent
from agents.Layer2.processing_agent import ProcessingAgent
from agents.Layer2.tts_agent import TTSAgent
from utils.redis_client import RedisClient
from schemas.a2a_message import A2AMessage, MessageType
from dotenv import load_dotenv
import uuid
import os
import time

load_dotenv()
AUDIO_FILES = [
    "fillerWords/لحضة واحدة مع حضرتك.mp3",
    "fillerWords/ثانية واحدة.mp3",
    "fillerWords/ثانية يا فندم.mp3"
]

async def agent_task(agent_name, session_id, process_logic):
    redis = RedisClient()
    instruction_channel = f"agent:{agent_name}:{session_id}"
    print(f"[{agent_name}] Listening on '{instruction_channel}'")

    async def callback(message_json):
        message = A2AMessage.from_json(message_json)
        print(f"[{agent_name}] Received instruction: {message.payload}")
        await process_logic(redis, session_id, message.payload)
        # For this test, stop after one instruction
        raise asyncio.CancelledError

    try:
        await redis.subscribe(instruction_channel, callback)
    except asyncio.CancelledError:
        print(f"[{agent_name}] Task complete, shutting down listener.")
    finally:
        await redis.close()

async def stt_process_logic(redis, session_id, payload):
    agent = STTAgent(session_id=session_id, state_id="test_state")
    with open(AUDIO_PATH, "rb") as f:
        audio_bytes = f.read()
    session_context = {"language": "ar", "response_format": "text"}
    result = await agent.process(audio_bytes, session_context=session_context)
    print("[STTAgent Output]:", result.model_dump())

async def preprocessing_process_logic(redis, session_id, payload):
    agent = PreprocessingAgent(session_id=session_id, state_id="test_state")
    result = await agent.process({})
    print("[PreprocessingAgent Output]:", result.model_dump())
    print(await agent.load_context(session_id),"Context after Preprocessing Agent ")

async def processing_process_logic(redis, session_id, payload):
    agent = ProcessingAgent(session_id=session_id, state_id="test_state")
    result = await agent.process({})
    print("[ProcessingAgent Output]:", result.model_dump())
    print(await agent.load_context(session_id),"Context after Processing Agent ")

async def tts_process_logic(redis, session_id, payload):
    agent = TTSAgent(session_id=session_id, state_id="test_state")
    result = await agent.process({})
    print("[TTSAgent Output]:", result.model_dump())
    audio_path = result.outputs.get("audio_path")
    print("[TTSAgent Audio Path]:", audio_path)
    print("Absolute audio path:", os.path.abspath(audio_path))
    # Play the audio if possible
    try:
        print("Waiting for audio file to be ready...")
        max_wait = 10  # seconds
        waited = 0
        file_ready = False
        while waited < max_wait:
            if audio_path and os.path.exists(audio_path) and os.path.getsize(audio_path) > 0:
                try:
                    with open(audio_path, "rb") as f:
                        f.read(10)  # Try to read a few bytes
                    file_ready = True
                    break
                except Exception:
                    pass
            time.sleep(0.2)
            waited += 0.2
        if not file_ready:
            print(f"Audio file {audio_path} not ready after waiting. Please play the file manually.")
        else:
            print("Playing audio...")
            playsound(audio_path)
    except ImportError:
        print("playsound not installed. Please play the audio file manually:", audio_path)
    except Exception as e:
        print(f"Could not play audio: {e}\nPlease play the file manually: {audio_path}")
    print(await agent.load_context(session_id),"Context after TTS Agent ")


async def orchestrator(redis, session_id):
    print("[Orchestrator] Started, listening for notifications...")
    async def callback(message_json):
        message = A2AMessage.from_json(message_json)
        if message.session_id != session_id or message.target_agent != "Orchestrator":
            return
        from_agent = message.source_agent
        print(f"[Orchestrator] Received notification from: {from_agent}")
        next_agent = None
        if from_agent == "stt_agent":
            next_agent = "preprocessing_agent"
        elif from_agent == "preprocessing_agent":
            next_agent = "processing_agent"
        elif from_agent == "processing_agent":
            next_agent = "tts_agent"
        elif from_agent == "tts_agent":
            print("[Orchestrator] Pipeline complete.")
            raise asyncio.CancelledError
        if next_agent:
            instruction = A2AMessage(
                session_id=session_id,
                message_type=MessageType.INSTRUCTION,
                source_agent="Orchestrator",
                target_agent=next_agent,
                payload={"action": "process"}
            )
            instruction_channel = f"agent:{next_agent}:{session_id}"
            print(f"[Orchestrator] Sending instruction to {next_agent} on {instruction_channel}")
            await redis.publish(instruction_channel, instruction.to_json())
    try:
        await redis.subscribe("agent_notifications", callback)
    except asyncio.CancelledError:
        print("[Orchestrator] Workflow finished, shutting down listener.")

if __name__ == "__main__":
    import time
    import uuid
    session_id = f"test_session_{uuid.uuid4().hex[:8]}"
    for i, audio_file in enumerate(AUDIO_FILES):
        print(f"\n===== Pipeline Run {i+1} =====")
        print(f"Using STT audio: {audio_file}")
        AUDIO_PATH = audio_file  # Set the global variable for this run
        start = time.time()
        # Pass the same session_id to main
        async def main_with_session():
            await main_with_id(session_id)
        # Define a new main_with_id that takes session_id
        import types
        if not hasattr(globals(), 'main_with_id'):
            async def main_with_id(session_id):
                redis = RedisClient()
                print(f"Session ID: {session_id}")
                stt_task = asyncio.create_task(agent_task("stt_agent", session_id, stt_process_logic))
                pre_task = asyncio.create_task(agent_task("preprocessing_agent", session_id, preprocessing_process_logic))
                proc_task = asyncio.create_task(agent_task("processing_agent", session_id, processing_process_logic))
                tts_task = asyncio.create_task(agent_task("tts_agent", session_id, tts_process_logic))
                orch_task = asyncio.create_task(orchestrator(redis, session_id))
                await asyncio.sleep(0.1)
                first_instruction = A2AMessage(
                    session_id=session_id,
                    message_type=MessageType.INSTRUCTION,
                    source_agent="System",
                    target_agent="stt_agent",
                    payload={"action": "process_audio", "source": "mic"}
                )
                await redis.publish(f"agent:stt_agent:{session_id}", first_instruction.to_json())
                await orch_task
                final_context = await redis.get(session_id)
                print("[Final Context in Redis]:", final_context)
                memory = MemoryManager(session_id)
                contextual = await memory.get_conversation()
                print("[Conversation History]:")
                print(contextual)
                stt_task.cancel()
                pre_task.cancel()
                proc_task.cancel()
                tts_task.cancel()
                await redis.close()
            globals()['main_with_id'] = main_with_id
        asyncio.run(main_with_session())
        print(f"Pipeline Run {i+1} completed in {time.time() - start:.2f} seconds.")
