import sys
import asyncio
import os
import time
from pathlib import Path
from dotenv import load_dotenv

# Add project root to path for imports
project_root = str(Path(__file__).parent.parent)
sys.path.append(project_root)

from schemas.outputSchema import StateOutput, StatusType, StatusCode
from core.state_mgmt.StateManager import StateManager
from asteval import Interpreter
from core.logger_config import setup_development_logging, get_module_logger, cleanup_logger

# Load variables from .env file into environment
load_dotenv()

# Set up logging for the main module
setup_development_logging()
logger = get_module_logger("main", session_id="demo_session")

def test_state_output_success():
    return StateOutput(
        status=StatusType.SUCCESS,
        message="Operation successful",
        code=StatusCode.OK,
        outputs={"data": "test"},
        meta={"source": "test_suite"}
    )

async def run_trial():
    """
    Runs a simple trial of the StateManager with the GenericBank workflow.
    """
    logger.info(
        "Starting StateManager trial run",
        action="run_trial",
        layer="demo"
    )

    try:
        sm = StateManager("GenericBank.json", "test_session", "user_1")

        # Step 1: Simulate greeting state
        logger.info(
            "=== Step 1: Greeting State ===",
            action="step_1_greeting",
            layer="demo"
        )
        input_data = {"intent": "check_balance", "slots": {"account_id": "12345"}}
        result = await sm.execute_step(input_data)
        logger.info(
            "Step 1 completed",
            action="step_1_greeting",
            output_data={
                "result_status": str(result.status),
                "current_state": sm.current_state_id
            },
            layer="demo"
        )

        # Step 2: Simulate check balance state
        logger.info(
            "=== Step 2: Check Balance State ===",
            action="step_2_check_balance",
            layer="demo"
        )
        input_data = {"account_id": "12345"}
        result = await sm.execute_step(input_data)
        logger.info(
            "Step 2 completed",
            action="step_2_check_balance",
            output_data={
                "result_status": str(result.status),
                "current_state": sm.current_state_id
            },
            layer="demo"
        )

        # Step 3: Simulate goodbye state
        logger.info(
            "=== Step 3: Goodbye State ===",
            action="step_3_goodbye",
            layer="demo"
        )
        input_data = {}
        result = await sm.execute_step(input_data)
        logger.info(
            "Step 3 completed",
            action="step_3_goodbye",
            output_data={
                "result_status": str(result.status),
                "current_state": sm.current_state_id
            },
            layer="demo"
        )

        logger.info(
            "StateManager trial run completed successfully",
            action="run_trial",
            layer="demo"
        )

    except Exception as e:
        logger.error(
            "Error during StateManager trial run",
            action="run_trial",
            reason=str(e),
            layer="demo"
        )
        raise

if __name__ == "__main__":
    try:
        logger.info(
            "Starting Voice Agents Platform demo",
            action="main",
            layer="demo"
        )
        asyncio.run(run_trial())
        logger.info(
            "Voice Agents Platform demo completed",
            action="main",
            layer="demo"
        )
    except Exception as e:
        logger.error(
            "Error in Voice Agents Platform demo",
            action="main",
            reason=str(e),
            layer="demo"
        )
        raise
    finally:
        # Clean up logger resources
        cleanup_logger()

    # sm = StateManager("GenericBank.json", "test_session","user_1")

    # state = sm.get_state("state_greeting")

    # print(sm.example_memory_usage())
    
    # --- Turn 1 ---
    # user_input_1 = "Hello, what's my balance?"
    # ai_response_1 = "Sure, I can check your balance. What's your account ID?"
    # sm.memory_manager.add_conversation_turn(user_input_1, ai_response_1, intent="greeting")
    # print(f"User: {user_input_1}")
    # print(f"AI: {ai_response_1}")
    # # This line shows what's currently in memory for contextual:
    # print("Current Contextual Memory after Turn 1:", sm.memory_manager.get_all_contextual())

    # time.sleep(1) # Simulate a 1-second pause

    # # --- Turn 2 ---
    # user_input_2 = "My account ID is 12345."
    # ai_response_2 = "Thanks! Your balance is $5000."
    # sm.memory_manager.add_conversation_turn(user_input_2, ai_response_2, intent="provide_account_id")
    # print(f"User: {user_input_2}")
    # print(f"AI: {ai_response_2}")
    # # This line shows what's currently in memory for contextual after the second turn:
    # print("Current Contextual Memory after Turn 2:", sm.memory_manager.get_all_contextual())

    # # This line saves the final in-memory contextual data to the JSON file:
    # sm.memory_manager.contextual.save_to_file("contextual_memory_log.json")
    # print("Final Contextual Memory saved to contextual_memory_log.json")

    # time.sleep(4)
    # sm.handle_intent("goodbye")
    # sm.memory_manager.set("persistent", "test", "test3")
    # sm.memory_manager.set("persistent", "test1", "test1")
    # print("Current Persistent Memory:", sm.memory_manager.get_all_persistent())
    # print("Loaded State:", state.id)
    # print(state)

    # context = {"intent" : "check_balance", "account_id": "12345"}
    # aeval = state.transitions[0].createAeval(context)

    # print(state.transitions[0])
    # print(state.transitions[0].evaluate(aeval))

    # layer2 = sm.get_layer2("l2_check_balance")
    # print("Loaded Layer2:", layer2.id)
    # print(layer2)

    # print(test_state_output_success())

    # # --- Execute the entire workflow step by step ---
    # print("\n--- Executing Workflow ---")
    # sm.is_running = True
    # step_count = 0
    # while sm.is_running:
    #     print(f"\nStep {step_count} (Current State: {sm.current_state_id})")
    #     output = sm.execute_step({})
    #     print("Step Output:", output)
    #     print(f"Next State: {sm.current_state_id}")
    #     step_count += 1
    # print("\nWorkflow execution complete.")





