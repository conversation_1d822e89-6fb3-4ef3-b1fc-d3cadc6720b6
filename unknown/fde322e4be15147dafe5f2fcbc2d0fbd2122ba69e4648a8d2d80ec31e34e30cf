{"workflow": {"name": "Generic Banking Support Flow", "version": "1.0", "start": "state_greeting", "states": {"state_greeting": {"id": "state_greeting", "type": "input", "layer2_id": "l2_greeting", "expected_input": ["intent", "slots"], "expected_output": ["acknowledgment", "transition_signal"], "transitions": [{"condition": "intent == 'check_balance'", "target": "state_check_balance"}, {"condition": "intent == 'loan_inquiry'", "target": "state_loan_inquiry"}], "allowed_tools": ["STT", "LLM", "TTS", "CACHE"]}, "state_check_balance": {"id": "state_check_balance", "type": "inform", "layer2_id": "l2_check_balance", "expected_input": ["account_id"], "expected_output": ["account_balance"], "transitions": [{"condition": "true", "target": "state_goodbye"}], "allowed_tools": ["LLM", "TTS"]}, "state_goodbye": {"id": "state_goodbye", "type": "end", "layer2_id": "l2_goodbye", "expected_input": [], "expected_output": ["exit_signal"], "allowed_tools": ["TTS"]}}}}