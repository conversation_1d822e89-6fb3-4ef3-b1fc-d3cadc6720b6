import asyncio
import logging

from utils.redis_client import RedisClient
from schemas.agent_metadata import AgentMetadata
from core.agent_registry import AgentRegistry, REGISTRY_REDIS_KEY

# Basic logging setup
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def clear_registry(redis: RedisClient):
    """Utility function to clear the agent registry in Redis for a clean demo."""
    logger.info("--- Clearing registry in Redis for a clean run ---")
    await redis.client.delete(REGISTRY_REDIS_KEY)

async def main():
    """
    A demonstration script to showcase the features of the AgentRegistry.
    """
    try:
        # --- This block will only run if Redis is available ---
        redis_client = RedisClient()
        await clear_registry(redis_client)
        
        # ====================================================================
        # 1. Initialize Registry
        # ====================================================================
        logger.info("\n--- 1. Initializing a new registry ---")
        
        # When Redis is empty, the registry should start with no agents.
        registry = AgentRegistry(redis_client)
        await registry.initialize()
        initial_agents = registry.list_all()
        logger.info(f"Registry initialized. Current number of agents: {len(initial_agents)}")

        # ====================================================================
        # 2. Register Agents & Check Persistence
        # ====================================================================
        logger.info("\n--- 2. Registering two new agents ---")

        # Register the first agent
        agent_a_meta = AgentMetadata(agent_name="TTSAgent", version="1.0", description="Text-to-Speech")
        logger.info(f"--> Registering '{agent_a_meta.agent_name}'...")
        await registry.register(agent_a_meta)
        
        # Register the second agent
        agent_b_meta = AgentMetadata(agent_name="STTAgent", version="1.1", description="Speech-to-Text")
        logger.info(f"--> Registering '{agent_b_meta.agent_name}'...")
        await registry.register(agent_b_meta)

        logger.info("Registration complete. Let's see what's in the cache:")
        for agent in registry.list_all():
            logger.info(f"  - Found in cache: {agent.agent_name} (Version: {agent.version})")

        # To demonstrate persistence, create a *new* registry instance.
        # It should load the agents we just registered from Redis.
        logger.info("\n--- Creating a second registry instance to show persistence ---")
        new_registry = AgentRegistry(redis_client)
        await new_registry.initialize()
        logger.info(f"Second registry initialized. It loaded {len(new_registry.list_all())} agents from Redis.")
        
        # ====================================================================
        # 3. List and Retrieve Agents
        # ====================================================================
        logger.info("\n--- 3. Listing and Retrieving Agents ---")
        
        # List all agents
        all_agents = new_registry.list_all()
        logger.info(f"--> Listing all agents ({len(all_agents)} found):")
        for agent in all_agents:
            logger.info(f"  - {agent.agent_name}")
        
        # Retrieve a specific agent
        logger.info("--> Retrieving 'TTSAgent' specifically...")
        tts_agent = new_registry.get("TTSAgent")
        if tts_agent:
            logger.info(f"Successfully retrieved '{tts_agent.agent_name}' (Version: {tts_agent.version}).")
        
        # Try to retrieve one that doesn't exist
        logger.info("--> Trying to retrieve a non-existent agent ('LLMAgent')...")
        non_existent_agent = new_registry.get("LLMAgent")
        if non_existent_agent is None:
            logger.info("As expected, 'LLMAgent' was not found.")

        # ====================================================================
        # 4. Update an Existing Agent
        # ====================================================================
        logger.info("\n--- 4. Updating an Existing Agent ---")
        
        logger.info("--> Updating 'TTSAgent' to version 2.0...")
        updated_agent_meta = AgentMetadata(agent_name="TTSAgent", version="2.0", description="An updated TTS agent")
        await new_registry.register(updated_agent_meta)
        
        # Verify the update
        updated_agent = new_registry.get("TTSAgent")
        logger.info(f"Retrieved '{updated_agent.agent_name}' again. New version is: {updated_agent.version}")
        
        # ====================================================================
        # 5. Unregister an Agent
        # ====================================================================
        logger.info("\n--- 5. Unregistering an Agent ---")
        
        logger.info(f"--> Unregistering '{agent_a_meta.agent_name}'...")
        await new_registry.unregister(agent_a_meta.agent_name)
        remaining_agents = new_registry.list_all()
        logger.info("--> Listing remaining agents:")
        for agent in remaining_agents:
            logger.info(f"  - {agent.agent_name}")


        await redis_client.close()
        logger.info("\n--- Redis Demo Finished ---")

    except Exception as e: # Catches Redis connection errors
        logger.warning(f"Could not connect to Redis: {e}")
        logger.warning("Skipping Redis-dependent demo parts.")

    # ====================================================================
    # 6. In-Memory Mode (No Redis)
    # ====================================================================
    logger.info("\n--- 6. Testing In-Memory Fallback Mode (use_redis=False) ---")

    # Instantiate the registry without a Redis client to run in-memory
    in_memory_registry = AgentRegistry()
    await in_memory_registry.initialize()
    logger.info("Registry initialized in in-memory mode.")

    # Register an agent
    logger.info("--> Registering 'InMemoryAgent'...")
    in_mem_agent = AgentMetadata(agent_name="InMemoryAgent", version="1.0", description="An agent running purely in memory.")
    await in_memory_registry.register(in_mem_agent)

    # List agents
    agents_in_memory = in_memory_registry.list_all()
    logger.info(f"Agents in memory: {[a.agent_name for a in agents_in_memory]}")

    # Get the agent
    retrieved = in_memory_registry.get("InMemoryAgent")
    if retrieved:
        logger.info(f"Successfully retrieved '{retrieved.agent_name}' from memory.")

    # Unregister
    logger.info("--> Unregistering 'InMemoryAgent'...")
    await in_memory_registry.unregister("InMemoryAgent")
    agents_after_unregister = in_memory_registry.list_all()
    logger.info(f"Agents remaining in memory: {len(agents_after_unregister)}")

    logger.info("\n--- In-Memory Demo Finished ---")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Main process interrupted.") 