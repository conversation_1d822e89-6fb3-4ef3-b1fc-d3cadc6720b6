import asyncio
import os
import json
import sys
from pathlib import Path
from typing import Dict, Any, Optional, List
from datetime import datetime, timezone

# Add project root to path for imports
project_root = str(Path(__file__).parent.parent.parent)
sys.path.append(project_root)

from core.exceptions import ConfigurationError, WorkflowError, Layer2Error
from core.logger import Logger, StateOutput as LoggerStateOutput
from core.logger_config import get_module_logger

from .WorkflowSchemaLoader import WorkflowSchemaLoader
from .Layer2SchemaLoader import Layer2SchemaLoader
from .states.Workflow import WorkflowWrapper,State
from .states.Layer2 import Layer2, PipelineStep
from schemas.outputSchema import StateOutput, StatusType, StatusCode
from asteval import Interpreter
from .memory_manager import MemoryManager
from .base_state import State
from .mcp_mock import MockMCPAgent

logger = get_module_logger("state_manager")

# Add config loader
def load_config():
    base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    config_path = os.path.join(base_dir, "config", "state_manager_config.json")
    try:
        with open(config_path, 'r') as f:
            return json.load(f)
    except Exception as e:
        logger.error(
            "Could not load config file",
            action="load_config",
            input_data={"config_path": config_path},
            reason=str(e),
            layer="configuration"
        )
        raise ConfigurationError(f"Failed to load state manager configuration: {e}")



class StateManager:
    """
    Main State Manager - the brain of the agent's execution loop.
    
    This class manages:
    - Workflow state transitions based on the provided schema format
    - Execution of Layer2 pipelines
    - Memory management 
    - Agent coordination (TODO: integrate with MCPAgentRegistry)
    """
    
    def __init__(self, workflow_name: str, session_id: str, user_id: Optional[str] = None):
        """
        Initialize the State Manager.
        
        Args:
            workflow_name: Name of the workflow to execute
            session_id: Unique session identifier
            user_id: Optional user identifier
            
        Raises:
            ConfigurationError: If configuration files cannot be loaded
            WorkflowError: If the workflow cannot be loaded or is invalid
            Layer2Error: If a Layer2 definition cannot be loaded or is invalid
        """
        self.workflow_name = workflow_name
        self.user_id = user_id
        self.session_id = session_id
        self.logger = get_module_logger("state_manager")
        base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        
        logger.info(
            "Initializing StateManager",
            action="initialize",
            input_data={
                "workflow": workflow_name,
                "session_id": session_id,
                "user_id": user_id
            },
            layer="state_management"
        )
        
        # Use config for path management
        try:
            self.config = load_config()
            workflow_states_dir = self.config["paths"]["workflow_states_dir"]
            self.workflow_file_path = os.path.join(base_dir, workflow_states_dir, workflow_name)
        except ConfigurationError as e:
            logger.error(
                "Configuration error during StateManager initialization",
                action="initialize",
                reason=str(e),
                layer="state_management"
            )
            raise
        
        self.workflow: WorkflowWrapper = None
        self.layer2_map: Dict[str, Layer2] = {}

        # MEMORY MANAGER INSTANTIATION
        self.memory_manager = MemoryManager(session_id, user_id)
        self.execution_history = []
        self.is_running = True
        self.current_state_id = None

        # TODO: Add MCPAgentRegistry integration
        self.agent_registry ={
                "stt_google": MockMCPAgent("stt_google"),
                "intent_classifier": MockMCPAgent("intent_classifier"),
                "llm_ack_gen": MockMCPAgent("llm_ack_gen"),
                "tts_google": MockMCPAgent("tts_google"),
                "validate_account_id": MockMCPAgent("validate_account_id"),
                "balance_fetcher": MockMCPAgent("balance_fetcher"),
                "llm_response_gen": MockMCPAgent("llm_response_gen"),
                "llm_farewell_gen": MockMCPAgent("llm_farewell_gen")
            }
        
        # self.agent_registry = MCPAgentRegistry()

        # Update logger context with session information
        logger.update_context(session_id=session_id, state_id="initialization")

        # Load workflow and Layer2 schemas
        try:
            self._load_workflow()
            self._load_all_layer2()
            # Set initial state
            if self.workflow and hasattr(self.workflow.workflow, 'start'):
                self.current_state_id = self.workflow.workflow.start
                logger.update_context(state_id=self.current_state_id)
                logger.info(
                    "StateManager initialization completed",
                    action="initialize",
                    output_data={"initial_state": self.current_state_id},
                    layer="state_management"
                )
        except Exception as e:
            logger.error(
                "Error initializing StateManager",
                action="initialize",
                reason=str(e),
                layer="state_management"
            )
            self.workflow = None

        print("r;ghroi;ghtretgrihtrio: ", self.workflow.workflow)
        self.current_state_id: str = self.workflow.workflow.start
        self.current_layer2_id_step: str = self.layer2_map.get(self.get_state(self.current_state_id).layer2_id).pipeline[0].step
    
    def _load_workflow(self):
        try:
            self.workflow = WorkflowSchemaLoader.load(self.workflow_file_path)
            if not self.workflow:
                raise Exception(f"Failed to load workflow: {self.workflow_file_path}")
        except Exception as e:
            logger.error(
                "Error loading workflow",
                action="_load_workflow",
                input_data={"workflow_file_path": self.workflow_file_path},
                reason=str(e),
                layer="state_management"
            )
            self.workflow = None

    def _load_all_layer2(self):
        if not self.workflow or not hasattr(self.workflow, 'workflow'):
            logger.warning(
                "No workflow loaded, cannot load Layer2 schemas",
                action="_load_all_layer2",
                layer="state_management"
            )
            return
        for state in self.workflow.workflow.states.values():
            layer2_id = getattr(state, 'layer2_id', None)
            if not layer2_id:
                continue
            # Get the absolute path using config
            base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            layer2_config_dir = self.config["paths"]["layer2_config_dir"]
            layer2_file = os.path.join(base_dir, layer2_config_dir, f"{layer2_id}.json")
            if not os.path.isfile(layer2_file):
                logger.warning(
                    "Layer2 file not found",
                    action="_load_all_layer2",
                    input_data={"layer2_file": layer2_file, "layer2_id": layer2_id},
                    layer="state_management"
                )
                raise Exception(f"Workflow contains references to non-existent Layer2 definitions {layer2_id}")
            try:
                layer2_obj = Layer2SchemaLoader.load(layer2_file, state.expected_output)
                if layer2_obj:
                    self.layer2_map[layer2_id] = layer2_obj
                else:
                    logger.warning(
                        "Failed to load Layer2",
                        action="_load_all_layer2",
                        input_data={"layer2_file": layer2_file, "layer2_id": layer2_id},
                        layer="state_management"
                    )
            except Exception as e:
                logger.error(
                    "Error loading Layer2",
                    action="_load_all_layer2",
                    input_data={"layer2_id": layer2_id, "layer2_file": layer2_file},
                    reason=str(e),
                    layer="state_management"
                )

    def get_state(self, state_id: str):
        return self.workflow.workflow.states.get(state_id)

    def get_layer2(self, layer2_id: str):
        return self.layer2_map.get(layer2_id)

    async def execute_step(self, input_data: dict) -> StateOutput:
        """
        Executes a single step of the workflow using the current state.
        """

        # Update logger context for current state
        logger.update_context(session_id=self.session_id, state_id=self.current_state_id)

        logger.info(
            "Starting state execution",
            action="execute_step",
            input_data={"current_state": self.current_state_id, "input": input_data},
            layer="state_management"
        )

        try :
            state_config = self.get_state(self.current_state_id)
            layer2_config = self.get_layer2(state_config.layer2_id)
            state = State(
                state_id=self.current_state_id,
                config=state_config,
                layer2_config=layer2_config,
                memory=self.memory_manager,
                tools_registry=self.agent_registry
            )
            result = await state.execute(input_data, {
                "session_id": self.session_id,
                "user_id": self.user_id,
                "account_id": "12345"
            })
            self.memory_manager.set("contextual", "output", result.outputs)
            # self.current_state_id = result.meta.get("next", self.current_state_id
            self.execution_history.append(result)
            previous_state = self.current_state_id
            
            logger.info(
                "State execution completed",
                action="execute_step",
                output_data={
                    "previous_state": previous_state,
                    "next_state": self.current_state_id,
                    "result_status": result.status.value if hasattr(result.status, 'value') else str(result.status)
                },
                layer="state_management",
                metrics=result.meta.get("metrics", {})
            )

            # Update logger context for new state
            logger.update_context(state_id=self.current_state_id)

            return result

        except Exception as e:
            logger.error(
                "Error during state execution",
                action="execute_step",
                input_data={"current_state": self.current_state_id, "input": input_data},
                reason=str(e),
                layer="state_management"
            )
            raise
    
    async def transition_to_next_state(self):
        """
        Transitions to the next state based on the current state's output.
        """
        current_state = self.get_state(self.current_state_id)
        if not current_state:
            raise WorkflowError(f"Current state '{self.current_state_id}' not found in workflow.")
        
        # Evaluate transitions
        aeval = Interpreter()
        for key, value in self.memory_manager.get("output").items():
            aeval.symtable[key] = value
        for output_key, output_value in self.memory_manager.get("output").items():
            aeval.symtable[output_key] = output_value
        if self.get_state(self.current_state_id).transitions.__len__() == 0:
            self.logger.warning("No transition conditions met", action="transition_to_next_state", input_data={"state_id": self.current_state_id}, layer="state_management")
            return None
        for transition in self.get_state(self.current_state_id).transitions:
            if transition.evaluate(aeval):
                next_state_id = transition.target
                self.logger.info("Transition condition met", action="transition_to_next_state", input_data={"state_id": self.current_state_id, "condition": transition.condition, "next_state": next_state_id}, layer="state_management")
                self.current_state_id = next_state_id
                return next_state_id
        self.logger.warning("No transition conditions met", action="transition_to_next_state", input_data={"state_id": self.current_state_id}, layer="state_management")
        return None

    def example_memory_usage(self):
        """
        Example of how to use the memory manager for all memory layers in StateManager.
        """
        # --- Ephemeral memory: store temporary pipeline data ---
        self.memory_manager.set("ephemeral", "transcribed_text", "Check my balance")
        # Retrieve ephemeral data
        transcribed = self.memory_manager.get("transcribed_text")
        # Clear ephemeral after pipeline
        self.memory_manager.clear_ephemeral()

        # --- Contextual memory: store session data and conversation ---
        self.memory_manager.set("contextual", "user_message", "Check my balance")
        self.memory_manager.set("contextual", "intent", "check_balance")
        self.memory_manager.set("contextual", "slots", {"account_id": "12345"})
        # Store conversation turns
        conversation = self.memory_manager.get("conversation") or []
        conversation.append({"role": "user", "text": "Check my balance 2"})
        conversation.append({"role": "ai", "text": "Sure, I'll check your balance 2"})
        self.memory_manager.set("contextual", "conversation", conversation)
        # Save contextual memory to file for logging/debugging
        self.memory_manager.contextual.save_to_file("contextual_memory_log.json")
        conversation.append({"role": "user", "text": "Check my balance 1"})
        conversation.append({"role": "ai", "text": "Sure, I'll check your balance 1"})
        self.memory_manager.set("contextual", "conversation", conversation)
        # Clear contextual at end of session
        # self.memory_manager.clear_contextual()
        # Log memory state for debugging
        logger.debug(
            "Current contextual memory state",
            action="example_memory_usage",
            output_data={
                "conversation": self.memory_manager.contextual.get("conversation"),
                "all_contextual": self.memory_manager.contextual.get_all()
            },
            layer="state_management"
        )

        # --- Persistent memory: store long-term user data ---
        self.memory_manager.set("persistent", "validated_account_id", "12345")
        self.memory_manager.set("persistent", "account_balance_history", {"2025-06-17": 5000})
        # Retrieve persistent data
        balance_history = self.memory_manager.get("account_balance_history")
        # Explicit memory saving (e.g., user says "Remember my preferred language is English")
        self.memory_manager.explicit_save("save_preference", {"preference": "language", "value": "en"})
        # Retrieve preference
        language = self.memory_manager.get("language")

    # TODO: Implement end_session_cleanup
    def end_session_cleanup(self):
        """Clear all session and ephemeral memory at the end of a conversation."""
        self.memory_manager.clear_contextual()
        self.memory_manager.clear_ephemeral()
        # Optionally, log or save the session before clearing
    # TODO: Implement handle_intent
    def handle_intent(self, intent):
        """Example handler to show when to clear memory."""
        if intent == "goodbye":
            self.end_session_cleanup()
            # Transition to end state, etc.
