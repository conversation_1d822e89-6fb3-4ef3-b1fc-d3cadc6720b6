version: '3.8'

services:
  redis:
    image: redis/redis-stack-server:latest
    container_name: ai_voice_agents_redis
    ports:
      - "6379:6379"  # Redis server
      - "8001:8001"  
    volumes:
      - redis_data:/data
    restart: unless-stopped
  mongodb:
    image: mongo:6.0
    container_name: mongodb
    restart: always
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: root
      MONGO_INITDB_ROOT_PASSWORD: example
    volumes:
      - mongo_data:/data/db

volumes:
  redis_data:
  mongo_data:
    

