# Voice Agents Platform - Repository Structure Documentation

This document provides a comprehensive overview of the Voice Agents Platform repository structure and the functionality of each file and directory.

## 📁 Repository Overview

The Voice Agents Platform is a modular, scalable voice AI system designed for low-latency execution with clean architecture. It follows a microservices approach with agent-based communication using Redis and MCP (Modular Communication Protocol).

## 🏗️ Root Level Files

### Configuration Files
- **`README.md`** - Main project documentation with architecture overview
- **`docker-compose.yml`** - Docker orchestration for Redis stack server (ports 6379, 8001)
- **`environment.yml`** - Conda environment specification with Python 3.13 and dependencies (OpenAI, Pydantic, Redis, Qdrant, etc.)

## 📂 Directory Structure

### 🧠 `agents/` - Core AI Agents
Contains the main AI agents responsible for different aspects of voice processing.

#### Files:
- **`README.md`** - Documentation for agent implementations
- **`base_agent.py`** - Abstract base classes for all agent types:
  - `BaseAgent` - Core agent interface with logging and context management
  - `AudioAgent` - Speech-to-text, text-to-speech, voice activity detection
  - `DisambiguatorAgent` - Language detection and text normalization
  - `ResponseAgent` - Intent parsing, RAG queries, LLM responses
  - `MCPAgent` - Modular Communication Protocol agent interface

### 🧩 `core/` - Infrastructure & Runtime Logic
The backbone of the system handling execution, memory, logging, and coordination.

#### Main Files:
- **`__main__.py`** - Main entry point for the application
- **`agent_registry.py`** - Agent registration and discovery system with Redis pub/sub
- **`exceptions.py`** - Custom exception hierarchy (VoiceAgentError, SchemaError, WorkflowError, etc.)
- **`logger.py`** - Structured JSONL logging system with thread-safe concurrent writes
- **`logger_config.py`** - Logger configuration settings
- **`logger_manual.md`** - Logging best practices guide
- **`contextual_memory_log.json`** - Contextual memory storage

#### Subdirectories:
- **`docs/`** - Core documentation
  - `state_output_guide.md` - Guide for state output handling
- **`logs/`** - Runtime log storage
  - `conversations/` - Conversation transcripts
  - `errors/` - Error logs
  - `metrics/` - Performance metrics
- **`state_mgmt/`** - State management system
  - `StateManager.py` - Main state manager for workflow execution
  - `WorkflowSchemaLoader.py` - Workflow configuration loader
  - `Layer2SchemaLoader.py` - Layer2 pipeline configuration loader
  - `base_state.py` - Base state interface
  - `layer2_pipeline.py` - Pipeline execution logic
  - `memory_manager.py` - Multi-layer memory management
  - `mcp_mock.py` - MCP protocol mock implementation
  - `states/` - Individual state implementations

### ⚙️ `config/` - Configuration Files
JSON configuration files for different system components.

#### Files:
- **`README.md`** - Configuration documentation
- **`state_manager_config.json`** - State manager configuration
- **`l2_greeting.json`** - Layer2 greeting pipeline configuration
- **`l2_goodbye.json`** - Layer2 goodbye pipeline configuration
- **`l2_check_balance.json`** - Layer2 balance check pipeline configuration
- **`l2_fallback.json`** - Layer2 fallback pipeline configuration

### 📊 `schemas/` - Data Models & Validation
Pydantic models for data validation and structure.

#### Files:
- **`__init__.py`** - Package initialization
- **`a2a_message.py`** - Agent-to-Agent message schema with MessageType enum
- **`agent_metadata.py`** - Agent metadata schema for registration
- **`outputSchema.py`** - Output schema definitions for state management

### 🛠️ `utils/` - Utility Functions
Shared helper functions and utilities used across the system.

#### Files:
- **`README.md`** - Utilities documentation
- **`audio_utils.py`** - Audio processing utilities (VAD, noise filtering, normalization)
- **`language_utils.py`** - Language detection, gender detection, translation helpers
- **`redis_client.py`** - Redis client wrapper with async support
- **`validation_utils.py`** - Data validation utilities

### 📜 `scripts/` - CLI Tools & Utilities
Command-line tools for development, testing, and maintenance.

#### Files:
- **`README.md`** - Scripts documentation
- **`redis_usage_example.py`** - Redis client usage examples
- **`registry_demo.py`** - Agent registry demonstration script

### ✅ `tests/` - Test Suite
Unit tests and integration tests for the platform.

#### Files:
- **`README.md`** - Testing documentation
- **`logger_test.py`** - Logger functionality tests

### 🪵 `logs/` - Runtime Logs
Centralized logging directory for all system logs.

#### Subdirectories:
- **`conversations/`** - Full conversation transcripts indexed by session ID
- **`errors/`** - Error logs with stack traces and metadata
- **`metrics/`** - Performance metrics (token usage, latency, session duration)

### 🌐 `frontend/` - User Interface
Web-based visualization and debugging tools.

#### Files:
- **`README.md`** - Frontend documentation (currently placeholder)

### 📚 `docs/` - Documentation
Additional documentation and guides.

#### Files:
- **`LOGGING_INTEGRATION_GUIDE.md`** - Comprehensive guide for integrating the custom logging system

### 🔄 `workflows/` - Workflow Definitions
Directory for workflow configuration files (currently empty).

### 💡 `examples/` - Example Implementations
Example code and usage patterns (currently empty).

## 🔧 Key Technologies & Dependencies

### Core Dependencies:
- **Python 3.13** - Main runtime
- **Redis** - Message bus and session storage
- **Pydantic** - Data validation and serialization
- **OpenAI** - LLM integration
- **Qdrant** - Vector database for RAG
- **NumPy** - Numerical computations
- **HTTPX** - Async HTTP client

### Development Tools:
- **Docker & Docker Compose** - Containerization
- **Conda** - Environment management
- **Pytest** - Testing framework

## 🏛️ Architecture Patterns

1. **Agent-Based Architecture** - Modular agents with specific responsibilities
2. **Event-Driven Communication** - Redis pub/sub for agent coordination
3. **State Management** - Centralized state management with workflow execution
4. **Structured Logging** - JSONL logging with rich metadata
5. **Memory Layers** - Multi-tier memory management (ephemeral, session, persistent)
6. **Schema-Driven** - Pydantic models for type safety and validation

## 🚀 Getting Started

1. **Environment Setup**: Use `environment.yml` to create conda environment
2. **Services**: Start Redis with `docker-compose up -d`
3. **Configuration**: Review and customize JSON configs in `config/`
4. **Logging**: Follow `docs/LOGGING_INTEGRATION_GUIDE.md` for logging setup
5. **Testing**: Run tests in `tests/` directory

This repository structure supports scalable voice AI applications with clear separation of concerns, robust error handling, and comprehensive logging for production deployments.
