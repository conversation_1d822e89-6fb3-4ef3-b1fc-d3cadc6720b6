from agents.base_agent import BaseAgent
from schemas.outputSchema import StateOutput, StatusType, StatusCode
from schemas.a2a_message import A2AMessage, MessageType
import asyncio
import os
import openai
import json
from tenacity import retry, stop_after_attempt, wait_fixed, retry_if_exception_type
from utils.audio_utils import synthesize_fallback_audio

class PreprocessingAgent(BaseAgent):
    def __init__(self, session_id=None, state_id=None):
        super().__init__("preprocessing_agent", session_id, state_id)
        openai_api_key = os.getenv("OPENAI_API_KEY")
        if not openai_api_key:
            raise ValueError("OPENAI_API_KEY environment variable is required")
        self.openai_client = openai.AsyncOpenAI(api_key=openai_api_key)

    def load_config(self):
        config_path = os.path.join(os.path.dirname(__file__), '../../..', 'config', 'state_manager_config.json')
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception:
            return {"enable_emotion": True, "enable_gender": True, "enable_disambiguation": False}

    async def process(self, input_data, context=None):
        start_time = asyncio.get_event_loop().time()
        context = context or {}
        self._log_process_start(input_data, context)
        session_id = self.session_id or context.get("session_id")
        redis_key = session_id
        config = self.load_config()
        try:
            # 1. Load the shared context dict
            shared_context = await self.load_context(redis_key) or {}
            fallback_result = await self.handle_redis_fallback(shared_context, session_id)
            if fallback_result:
                return fallback_result
            # 1a. Check for Redis fallback (e.g., tts_response)
            if isinstance(shared_context, dict) and "tts_response" in shared_context:
                return StateOutput(
                    status=StatusType.ERROR,
                    message="Redis unavailable. Fallback triggered.",
                    code=StatusCode.SERVICE_UNAVAILABLE,
                    outputs={"fallback_message": shared_context["tts_response"]},
                    meta={"agent": self.agent_name}
                )
            # 2. Get the text to preprocess
            text = shared_context.get("transcript")
            print("some thing to see ",text)
            if not text:
                return StateOutput(
                    status=StatusType.ERROR,
                    message="No transcript found in shared context",
                    code=StatusCode.NOT_FOUND,
                    outputs={},
                    meta={"agent": self.agent_name}
                )
            # 3. Preprocess (clean, intent, emotion, gender)
            clean_text = await self.clean_text(text)
            intent = await self.classify_intent(clean_text)
            emotion = None
            gender = None
            if config.get("enable_emotion", True):
                emotion = await self.detect_emotion(clean_text)
            if config.get("enable_gender", True):
                gender = await self.detect_gender(clean_text)
            duration_ms = int((asyncio.get_event_loop().time() - start_time) * 1000)
            outputs = {
                "intent": intent,
                "clean_text": clean_text
            }
            if emotion is not None:
                outputs["emotion"] = emotion
            if gender is not None:
                outputs["gender"] = gender
            # 4. Update and save the shared context, including latency
            # Always reload the latest context before saving
            latest_context = await self.load_context(redis_key) or {}
            latest_context.update(outputs)
            latest_context["latencyPreprocessing"] = duration_ms
            await self.save_context(redis_key, latest_context)
            # 5. Publish notification
            notification = A2AMessage(
                session_id=session_id,
                message_type=MessageType.NOTIFICATION,
                source_agent=self.agent_name,
                target_agent="Orchestrator",
                payload={"status": "complete"},
                context_keys_updated=list(outputs.keys()) + ["latencyPreprocessing"]
            )
            await self.publish_notification("agent_notifications", notification.to_json())
            # 6. Return result
            result = StateOutput(
                status=StatusType.SUCCESS,
                message="Text preprocessed successfully",
                code=StatusCode.OK,
                outputs=outputs,
                meta={
                    "agent": self.agent_name,
                    "original_length": len(text),
                    "cleaned_length": len(clean_text),
                    "latencyPreprocessing": duration_ms
                }
            )
            self._log_process_end(result, duration_ms)
            return result
        except Exception as e:
            duration_ms = int((asyncio.get_event_loop().time() - start_time) * 1000)
            self._log_error(e, input_data)
            # Notify orchestrator of error
            notification = A2AMessage(
                session_id=session_id,
                message_type=MessageType.NOTIFICATION,
                source_agent=self.agent_name,
                target_agent="Orchestrator",
                payload={"status": "error", "error_message": str(e)},
                context_keys_updated=[]
            )
            await self.publish_notification("agent_notifications", notification.to_json())
            # Generate fallback audio
            fallback_text = "Sorry, something went wrong. Can you repeat that please?"
            audio_path = synthesize_fallback_audio(fallback_text, session_id=session_id or "fallback")
            outputs = {"fallback_message": fallback_text}
            if audio_path:
                outputs["audio_path"] = audio_path
            return StateOutput(
                status=StatusType.ERROR,
                message=f"PreprocessingAgent error: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs=outputs,
                meta={
                    "agent": self.agent_name,
                    "latencyPreprocessing": duration_ms,
                    "error": str(e)
                }
            )

    # TODO Clean test using Disambiguation and Noise Filtering 
    async def clean_text(self, text: str) -> str:
        return " ".join(text.strip().lower().split())

    @retry(stop=stop_after_attempt(3), wait=wait_fixed(1), retry=retry_if_exception_type(Exception))
    async def classify_intent(self, text: str) -> str:
        prompt = (
            "Given the following user message, identify the user's intent as a single word or short phrase "
            "(e.g., inquire_balance, loan_inquiry, greeting, complaint, unknown). "
            "If the intent is unclear, respond with 'unknown'.\n"
            f"Message: {text}\nIntent:"
        )
        try:
            response = await self.openai_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=5,
                temperature=0.2
            )
            intent = response.choices[0].message.content.strip().lower()
            return intent
        except Exception as e:
            self.logger.warning(f"Intent classification failed: {e}")
            return "unknown"

    @retry(stop=stop_after_attempt(3), wait=wait_fixed(1), retry=retry_if_exception_type(Exception))
    async def detect_emotion(self, text: str):
        prompt = f"What is the primary emotion expressed in the following text? Respond with one word (e.g., neutral, happy, sad, angry, excited, etc.).\nText: {text}"
        try:
            response = await self.openai_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=1,
                temperature=0.3
            )
            emotion = response.choices[0].message.content.strip().lower()
            return emotion
        except Exception as e:
            self.logger.warning(f"Emotion detection failed: {e}")
            return None

    # Note we should save the gender in memory so these function does not run every time user speaks
    @retry(stop=stop_after_attempt(3), wait=wait_fixed(1), retry=retry_if_exception_type(Exception))
    async def detect_gender(self, text: str):
        prompt = f"Based on the following text, what is the likely gender of the speaker? Respond with one word (male, female, unknown).\nText: {text}"
        try:
            response = await self.openai_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=1,
                temperature=0.3
            )
            gender = response.choices[0].message.content.strip().lower()
            return gender
        except Exception as e:
            self.logger.warning(f"Gender detection failed: {e}")
            return None 