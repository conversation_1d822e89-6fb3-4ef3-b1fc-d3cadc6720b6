"""
Base agent classes for the Voice Agents Platform.

This module provides abstract base classes and common functionality for all agents
in the Voice Agents Platform, including integrated logging, error handling, and
standardized interfaces.
"""

import sys
from pathlib import Path
from abc import ABC, abstractmethod
from typing import Any, Dict, Optional, List, Union
import asyncio
import time
from datetime import datetime, timezone

# Add project root to path for imports
project_root = str(Path(__file__).parent.parent)
sys.path.append(project_root)

from core.logger_config import get_module_logger
from schemas.outputSchema import StateOutput, StatusType, StatusCode


class BaseAgent(ABC):
    """
    Abstract base class for all agents in the Voice Agents Platform.
    
    Provides common functionality including:
    - Integrated structured logging
    - Error handling and metrics collection
    - Standardized input/output interfaces
    - Session and state context management
    """
    
    def __init__(self, agent_name: str, session_id: str = None, state_id: str = None):
        """
        Initialize the base agent.
        
        Args:
            agent_name: Unique name for this agent
            session_id: Optional session ID for context
            state_id: Optional state ID for context
        """
        self.agent_name = agent_name
        self.session_id = session_id
        self.state_id = state_id
        self.logger = get_module_logger(agent_name, session_id, state_id)
        self.metrics = {}
        
        self.logger.info(
            f"Initialized {agent_name} agent",
            action="initialize",
            layer="agent",
            agent_name=agent_name
        )
    
    def update_context(self, session_id: str = None, state_id: str = None):
        """Update the agent's session and state context."""
        if session_id:
            self.session_id = session_id
        if state_id:
            self.state_id = state_id
        self.logger.update_context(session_id, state_id)
    
    @abstractmethod
    async def process(self, input_data: Dict[str, Any], context: Dict[str, Any] = None) -> StateOutput:
        """
        Process input data and return a StateOutput.
        
        Args:
            input_data: Input data to process
            context: Optional context information
            
        Returns:
            StateOutput: Processed result with status, data, and metadata
        """
        pass
    
    def _log_process_start(self, input_data: Dict[str, Any], context: Dict[str, Any] = None):
        """Log the start of processing."""
        self.logger.info(
            f"Starting {self.agent_name} processing",
            action="process_start",
            input_data={"input": input_data, "context": context},
            layer="agent",
            agent_name=self.agent_name
        )
    
    def _log_process_end(self, result: StateOutput, duration_ms: float):
        """Log the end of processing."""
        self.logger.info(
            f"Completed {self.agent_name} processing",
            action="process_end",
            output_data={
                "status": result.status.value if hasattr(result.status, 'value') else str(result.status),
                "message": result.message
            },
            metrics={"duration_ms": duration_ms},
            layer="agent",
            agent_name=self.agent_name
        )
    
    def _log_error(self, error: Exception, input_data: Dict[str, Any] = None):
        """Log an error during processing."""
        self.logger.error(
            f"Error in {self.agent_name} processing",
            action="process_error",
            input_data={"input": input_data} if input_data else None,
            reason=str(error),
            layer="agent",
            agent_name=self.agent_name
        )
    
    async def safe_process(self, input_data: Dict[str, Any], context: Dict[str, Any] = None) -> StateOutput:
        """
        Safely process input with error handling and logging.
        
        Args:
            input_data: Input data to process
            context: Optional context information
            
        Returns:
            StateOutput: Processed result or error state
        """
        start_time = time.time()
        
        try:
            self._log_process_start(input_data, context)
            result = await self.process(input_data, context)
            duration_ms = (time.time() - start_time) * 1000
            self._log_process_end(result, duration_ms)
            return result
            
        except Exception as e:
            duration_ms = (time.time() - start_time) * 1000
            self._log_error(e, input_data)
            
            return StateOutput(
                status=StatusType.ERROR,
                message=f"Error in {self.agent_name}: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={
                    "error": str(e),
                    "agent": self.agent_name,
                    "duration_ms": duration_ms
                }
            )


class VoiceAgent(BaseAgent):
    """
    Base class for voice-related agents.
    
    Orchestrator agent that manages the voice interaction loop,
    queries other agents, and routes decisions.
    """
    
    def __init__(self, session_id: str = None, state_id: str = None):
        super().__init__("voice_agent", session_id, state_id)
    
    @abstractmethod
    async def process_voice_interaction(self, audio_data: bytes, context: Dict[str, Any] = None) -> StateOutput:
        """Process a complete voice interaction."""
        pass


class AudioAgent(BaseAgent):
    """
    Base class for audio processing agents.
    
    Handles speech-to-text (STT), voice activity detection (VAD),
    audio cleaning, and text-to-speech (TTS).
    """
    
    def __init__(self, session_id: str = None, state_id: str = None):
        super().__init__("audio_agent", session_id, state_id)
    
    @abstractmethod
    async def speech_to_text(self, audio_data: bytes) -> StateOutput:
        """Convert speech to text."""
        pass
    
    @abstractmethod
    async def text_to_speech(self, text: str, voice_config: Dict[str, Any] = None) -> StateOutput:
        """Convert text to speech."""
        pass
    
    @abstractmethod
    async def detect_voice_activity(self, audio_data: bytes) -> StateOutput:
        """Detect voice activity in audio."""
        pass


class DisambiguatorAgent(BaseAgent):
    """
    Base class for language disambiguation agents.
    
    Fixes language issues, determines language and gender,
    normalizes ambiguous phrases, and supports multilingual conversion.
    """
    
    def __init__(self, session_id: str = None, state_id: str = None):
        super().__init__("disambiguator", session_id, state_id)
    
    @abstractmethod
    async def detect_language(self, text: str) -> StateOutput:
        """Detect the language of input text."""
        pass
    
    @abstractmethod
    async def normalize_text(self, text: str, language: str = None) -> StateOutput:
        """Normalize and clean input text."""
        pass


class ResponseAgent(BaseAgent):
    """
    Base class for response generation agents.
    
    Parses intents, triggers RAG if needed, queries LLM,
    and returns appropriate responses.
    """
    
    def __init__(self, session_id: str = None, state_id: str = None):
        super().__init__("response_agent", session_id, state_id)
    
    @abstractmethod
    async def parse_intent(self, text: str, context: Dict[str, Any] = None) -> StateOutput:
        """Parse intent from input text."""
        pass
    
    @abstractmethod
    async def generate_response(self, intent: str, entities: Dict[str, Any], context: Dict[str, Any] = None) -> StateOutput:
        """Generate response based on intent and entities."""
        pass
    
    @abstractmethod
    async def query_knowledge_base(self, query: str, context: Dict[str, Any] = None) -> StateOutput:
        """Query knowledge base for relevant information."""
        pass


class MCPAgent(BaseAgent):
    """
    Base class for MCP (Modular Communication Protocol) agents.
    
    Provides standardized communication interface for agent-to-agent
    messaging and external service integration.
    """
    
    def __init__(self, agent_name: str, session_id: str = None, state_id: str = None):
        super().__init__(f"mcp_{agent_name}", session_id, state_id)
        self.mcp_capabilities = []
    
    @abstractmethod
    async def handle_mcp_message(self, message: Dict[str, Any]) -> StateOutput:
        """Handle incoming MCP message."""
        pass
    
    def register_capability(self, capability: str):
        """Register an MCP capability."""
        if capability not in self.mcp_capabilities:
            self.mcp_capabilities.append(capability)
            self.logger.info(
                f"Registered MCP capability: {capability}",
                action="register_capability",
                output_data={"capability": capability, "total_capabilities": len(self.mcp_capabilities)},
                layer="agent",
                agent_name=self.agent_name
            )
