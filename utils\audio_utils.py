"""
Audio utilities for the Voice Agents Platform.

This module provides audio processing utilities including format conversion,
voice activity detection, audio quality analysis, and audio preprocessing
with integrated structured logging.
"""

import sys
from pathlib import Path
from typing import Dict, Any, Optional, List, Tuple
import io
import wave
import struct
import math

# Add project root to path for imports
project_root = str(Path(__file__).parent.parent)
sys.path.append(project_root)

from core.logger_config import get_module_logger
from schemas.outputSchema import StateOutput, StatusType, StatusCode

# Module logger
logger = get_module_logger("audio_utils")


class AudioProcessor:
    """Audio processing utilities with logging."""
    
    def __init__(self):
        self.supported_formats = ['wav', 'mp3', 'flac', 'ogg']
        self.sample_rates = [8000, 16000, 22050, 44100, 48000]
        
        logger.info(
            "Initialized AudioProcessor",
            action="initialize",
            output_data={
                "supported_formats": self.supported_formats,
                "supported_sample_rates": self.sample_rates
            },
            layer="audio_utils"
        )
    
    def analyze_audio_properties(self, audio_data: bytes, session_id: str = None) -> StateOutput:
        """
        Analyze audio properties like format, duration, sample rate, etc.
        
        Args:
            audio_data: Raw audio data
            session_id: Optional session ID for context
            
        Returns:
            StateOutput with audio properties
        """
        try:
            logger.info(
                "Starting audio analysis",
                action="analyze_audio",
                input_data={"audio_size_bytes": len(audio_data)},
                layer="audio_utils",
                session_id=session_id
            )
            
            if not audio_data:
                return StateOutput(
                    status=StatusType.ERROR,
                    message="Empty audio data provided",
                    code=StatusCode.BAD_REQUEST,
                    outputs={},
                    meta={"error": "empty_audio"}
                )
            
            # Try to parse as WAV file
            try:
                audio_io = io.BytesIO(audio_data)
                with wave.open(audio_io, 'rb') as wav_file:
                    properties = {
                        "format": "wav",
                        "channels": wav_file.getnchannels(),
                        "sample_rate": wav_file.getframerate(),
                        "sample_width": wav_file.getsampwidth(),
                        "frames": wav_file.getnframes(),
                        "duration_seconds": wav_file.getnframes() / wav_file.getframerate(),
                        "size_bytes": len(audio_data)
                    }
                    
                    # Calculate additional metrics
                    properties["bitrate"] = properties["sample_rate"] * properties["sample_width"] * 8 * properties["channels"]
                    properties["is_stereo"] = properties["channels"] == 2
                    properties["is_mono"] = properties["channels"] == 1
                    
            except wave.Error:
                # If not WAV, provide basic analysis
                properties = {
                    "format": "unknown",
                    "size_bytes": len(audio_data),
                    "estimated_duration": len(audio_data) / 32000,  # Rough estimate
                    "analysis_note": "Could not parse as WAV file"
                }
            
            logger.info(
                "Audio analysis completed",
                action="analyze_audio",
                output_data=properties,
                layer="audio_utils",
                session_id=session_id
            )
            
            return StateOutput(
                status=StatusType.SUCCESS,
                message="Audio analysis completed",
                code=StatusCode.OK,
                outputs=properties,
                meta={"format": properties.get("format", "unknown")}
            )
            
        except Exception as e:
            logger.error(
                "Error in audio analysis",
                action="analyze_audio",
                input_data={"audio_size_bytes": len(audio_data) if audio_data else 0},
                reason=str(e),
                layer="audio_utils",
                session_id=session_id
            )
            
            return StateOutput(
                status=StatusType.ERROR,
                message=f"Audio analysis failed: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"error": str(e)}
            )
    
    def detect_voice_activity(self, audio_data: bytes, threshold: float = 0.01, session_id: str = None) -> StateOutput:
        """
        Detect voice activity in audio data.
        
        Args:
            audio_data: Raw audio data
            threshold: Energy threshold for voice detection
            session_id: Optional session ID for context
            
        Returns:
            StateOutput with voice activity detection results
        """
        try:
            logger.info(
                "Starting voice activity detection",
                action="detect_voice_activity",
                input_data={
                    "audio_size_bytes": len(audio_data),
                    "threshold": threshold
                },
                layer="audio_utils",
                session_id=session_id
            )
            
            if not audio_data:
                return StateOutput(
                    status=StatusType.ERROR,
                    message="Empty audio data provided",
                    code=StatusCode.BAD_REQUEST,
                    outputs={},
                    meta={"error": "empty_audio"}
                )
            
            # Simple energy-based VAD
            try:
                audio_io = io.BytesIO(audio_data)
                with wave.open(audio_io, 'rb') as wav_file:
                    frames = wav_file.readframes(wav_file.getnframes())
                    sample_width = wav_file.getsampwidth()
                    
                    # Convert to samples
                    if sample_width == 1:
                        samples = struct.unpack(f'{len(frames)}B', frames)
                        samples = [(s - 128) / 128.0 for s in samples]  # Convert to -1 to 1
                    elif sample_width == 2:
                        samples = struct.unpack(f'{len(frames)//2}h', frames)
                        samples = [s / 32768.0 for s in samples]  # Convert to -1 to 1
                    else:
                        raise ValueError(f"Unsupported sample width: {sample_width}")
                    
                    # Calculate energy in windows
                    window_size = 1024
                    windows = []
                    for i in range(0, len(samples), window_size):
                        window = samples[i:i+window_size]
                        energy = sum(s*s for s in window) / len(window)
                        windows.append(energy)
                    
                    # Detect voice activity
                    voice_windows = [e > threshold for e in windows]
                    voice_ratio = sum(voice_windows) / len(voice_windows) if voice_windows else 0
                    
                    result = {
                        "has_voice": voice_ratio > 0.1,  # At least 10% voice activity
                        "voice_ratio": voice_ratio,
                        "total_windows": len(windows),
                        "voice_windows": sum(voice_windows),
                        "average_energy": sum(windows) / len(windows) if windows else 0,
                        "max_energy": max(windows) if windows else 0,
                        "threshold_used": threshold
                    }
                    
            except wave.Error:
                # Fallback: simple amplitude analysis
                if len(audio_data) < 100:
                    result = {
                        "has_voice": False,
                        "voice_ratio": 0.0,
                        "error": "Audio too short for analysis"
                    }
                else:
                    # Very basic analysis on raw bytes
                    avg_amplitude = sum(audio_data) / len(audio_data)
                    result = {
                        "has_voice": avg_amplitude > 50,  # Arbitrary threshold
                        "voice_ratio": min(avg_amplitude / 128.0, 1.0),
                        "average_amplitude": avg_amplitude,
                        "analysis_note": "Fallback analysis on raw audio data"
                    }
            
            logger.info(
                "Voice activity detection completed",
                action="detect_voice_activity",
                output_data=result,
                layer="audio_utils",
                session_id=session_id
            )
            
            return StateOutput(
                status=StatusType.SUCCESS,
                message=f"Voice activity: {'detected' if result['has_voice'] else 'not detected'}",
                code=StatusCode.OK,
                outputs=result,
                meta={"has_voice": result["has_voice"]}
            )
            
        except Exception as e:
            logger.error(
                "Error in voice activity detection",
                action="detect_voice_activity",
                input_data={"audio_size_bytes": len(audio_data) if audio_data else 0},
                reason=str(e),
                layer="audio_utils",
                session_id=session_id
            )
            
            return StateOutput(
                status=StatusType.ERROR,
                message=f"Voice activity detection failed: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"error": str(e)}
            )
    
    def validate_audio_quality(self, audio_data: bytes, session_id: str = None) -> StateOutput:
        """
        Validate audio quality and provide recommendations.
        
        Args:
            audio_data: Raw audio data
            session_id: Optional session ID for context
            
        Returns:
            StateOutput with quality assessment
        """
        try:
            logger.info(
                "Starting audio quality validation",
                action="validate_quality",
                input_data={"audio_size_bytes": len(audio_data)},
                layer="audio_utils",
                session_id=session_id
            )
            
            # Get audio properties first
            props_result = self.analyze_audio_properties(audio_data, session_id)
            if props_result.status != StatusType.SUCCESS:
                return props_result
            
            properties = props_result.outputs
            quality_issues = []
            recommendations = []
            quality_score = 100  # Start with perfect score
            
            # Check sample rate
            if properties.get("sample_rate", 0) < 16000:
                quality_issues.append("Low sample rate")
                recommendations.append("Use at least 16kHz sample rate for better quality")
                quality_score -= 20
            
            # Check duration
            duration = properties.get("duration_seconds", 0)
            if duration < 0.5:
                quality_issues.append("Audio too short")
                recommendations.append("Provide at least 0.5 seconds of audio")
                quality_score -= 30
            elif duration > 30:
                quality_issues.append("Audio very long")
                recommendations.append("Consider splitting long audio into smaller chunks")
                quality_score -= 10
            
            # Check channels
            if properties.get("channels", 0) > 2:
                quality_issues.append("Too many channels")
                recommendations.append("Use mono or stereo audio")
                quality_score -= 15
            
            # Check file size
            size_mb = properties.get("size_bytes", 0) / (1024 * 1024)
            if size_mb > 10:
                quality_issues.append("Large file size")
                recommendations.append("Consider compressing audio or reducing quality")
                quality_score -= 10
            
            quality_score = max(quality_score, 0)  # Don't go below 0
            
            result = {
                "quality_score": quality_score,
                "quality_grade": "A" if quality_score >= 90 else "B" if quality_score >= 70 else "C" if quality_score >= 50 else "D",
                "issues": quality_issues,
                "recommendations": recommendations,
                "properties": properties,
                "is_acceptable": quality_score >= 50
            }
            
            logger.info(
                "Audio quality validation completed",
                action="validate_quality",
                output_data={
                    "quality_score": quality_score,
                    "quality_grade": result["quality_grade"],
                    "issues_count": len(quality_issues)
                },
                layer="audio_utils",
                session_id=session_id
            )
            
            return StateOutput(
                status=StatusType.SUCCESS,
                message=f"Audio quality: {result['quality_grade']} (Score: {quality_score})",
                code=StatusCode.OK,
                outputs=result,
                meta={"quality_score": quality_score}
            )
            
        except Exception as e:
            logger.error(
                "Error in audio quality validation",
                action="validate_quality",
                input_data={"audio_size_bytes": len(audio_data) if audio_data else 0},
                reason=str(e),
                layer="audio_utils",
                session_id=session_id
            )
            
            return StateOutput(
                status=StatusType.ERROR,
                message=f"Audio quality validation failed: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"error": str(e)}
            )


# Convenience functions for easy access
def analyze_audio(audio_data: bytes, session_id: str = None) -> StateOutput:
    """Convenience function for audio analysis."""
    processor = AudioProcessor()
    return processor.analyze_audio_properties(audio_data, session_id)


def detect_voice_activity(audio_data: bytes, threshold: float = 0.01, session_id: str = None) -> StateOutput:
    """Convenience function for voice activity detection."""
    processor = AudioProcessor()
    return processor.detect_voice_activity(audio_data, threshold, session_id)


def validate_audio_quality(audio_data: bytes, session_id: str = None) -> StateOutput:
    """Convenience function for audio quality validation."""
    processor = AudioProcessor()
    return processor.validate_audio_quality(audio_data, session_id)
